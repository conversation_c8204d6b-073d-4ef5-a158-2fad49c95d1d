import React, { useState } from 'react';
import type { PersonalInfo } from '../types/cv';
import { useActiveSection } from '../hooks/useActiveSection';

interface HeaderProps {
  personalInfo: PersonalInfo;
}

const Header: React.FC<HeaderProps> = ({ personalInfo }) => {
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const activeSection = useActiveSection();

  const toggleMobileNav = () => {
    setMobileNavOpen(!mobileNavOpen);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileNavOpen(false);
  };

  return (
    <>
      {/* Mobile nav toggle button */}
      <i
        className={`bi ${mobileNavOpen ? 'bi-x' : 'bi-list'} mobile-nav-toggle d-xl-none`}
        onClick={toggleMobileNav}
      ></i>

      {/* Header */}
      <header id="header" className={mobileNavOpen ? 'header-show' : ''}>
        <div className="d-flex flex-column">
          <div className="profile">
            <img
              src={personalInfo.logo}
              alt={personalInfo.name}
              className="img-fluid rounded-circle"
            />
            <h1 className="text-light">
              <a href="#hero" onClick={(e) => { e.preventDefault(); scrollToSection('hero'); }}>
                {personalInfo.name}
              </a>
            </h1>
            <div className="social-links mt-3 text-center">
              {personalInfo.socialLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={link.platform}
                >
                  <i className={link.icon}></i>
                </a>
              ))}
            </div>
          </div>

          <nav id="navbar" className="nav-menu navbar">
            <ul>
              <li>
                <a
                  href="#hero"
                  className={`nav-link scrollto ${activeSection === 'hero' ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); scrollToSection('hero'); }}
                >
                  <i className="bx bx-home"></i>
                  <span>Home</span>
                </a>
              </li>
              <li>
                <a
                  href="#about"
                  className={`nav-link scrollto ${activeSection === 'about' ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); scrollToSection('about'); }}
                >
                  <i className="bx bx-user"></i>
                  <span>Sobre mi</span>
                </a>
              </li>
              <li>
                <a
                  href="#resume"
                  className={`nav-link scrollto ${activeSection === 'resume' ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); scrollToSection('resume'); }}
                >
                  <i className="bx bx-file-blank"></i>
                  <span>Experiencia</span>
                </a>
              </li>
              <li>
                <a
                  href="#portfolio"
                  className={`nav-link scrollto ${activeSection === 'portfolio' ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); scrollToSection('portfolio'); }}
                >
                  <i className="bx bx-book-content"></i>
                  <span>Portfolio</span>
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  className={`nav-link scrollto ${activeSection === 'services' ? 'active' : ''}`}
                  onClick={(e) => { e.preventDefault(); scrollToSection('services'); }}
                >
                  <i className="bx bx-server"></i>
                  <span>Servicios</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </header>
    </>
  );
};

export default Header;
